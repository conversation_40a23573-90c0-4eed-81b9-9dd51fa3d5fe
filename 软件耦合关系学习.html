<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件耦合关系互动学习</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .learning-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .concept-intro {
            text-align: center;
            margin-bottom: 40px;
        }

        .concept-intro h2 {
            color: #4a5568;
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .coupling-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .coupling-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .coupling-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .coupling-card.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .demo-area {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .modules-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            min-height: 300px;
            position: relative;
        }

        .module {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .module-a { background: #e53e3e; }
        .module-b { background: #38a169; }
        .module-c { background: #3182ce; }
        .module-d { background: #d69e2e; }
        .module-e { background: #805ad5; }

        .shared-data {
            width: 120px;
            height: 60px;
            background: #2d3748;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9em;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background: #4299e1;
            opacity: 0;
            transform-origin: left center;
        }

        .interface-table {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.2em;
            margin-bottom: 20px;
            color: #2d3748;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #4299e1;
            transform: translateY(-2px);
        }

        .quiz-option.selected {
            background: #4299e1;
            color: white;
            border-color: #4299e1;
        }

        .quiz-option.correct {
            background: #48bb78;
            color: white;
            border-color: #48bb78;
        }

        .quiz-option.wrong {
            background: #f56565;
            color: white;
            border-color: #f56565;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(66, 153, 225, 0.5); }
            50% { box-shadow: 0 0 20px rgba(66, 153, 225, 0.8); }
        }

        .glow {
            animation: glow 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 软件耦合关系互动学习</h1>
            <p>从零开始，轻松掌握软件模块间的耦合关系</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 概念介绍 -->
        <div class="learning-section" id="conceptSection">
            <div class="concept-intro">
                <h2>🤔 什么是软件耦合？</h2>
                <p>想象一下，软件模块就像乐高积木，它们之间的连接方式就是"耦合"！</p>
            </div>

            <div class="coupling-types">
                <div class="coupling-card" data-type="data">
                    <h3>📊 数据耦合</h3>
                    <p>模块间只传递简单数据</p>
                </div>
                <div class="coupling-card" data-type="common">
                    <h3>🏠 公共耦合</h3>
                    <p>多个模块共享同一数据区</p>
                </div>
                <div class="coupling-card" data-type="content">
                    <h3>🔗 内容耦合</h3>
                    <p>一个模块直接访问另一个模块内部</p>
                </div>
                <div class="coupling-card" data-type="none">
                    <h3>🚫 无耦合</h3>
                    <p>模块间没有任何关系</p>
                </div>
            </div>

            <button class="btn" onclick="startDemo()">🚀 开始互动演示</button>
        </div>

        <!-- 演示区域 -->
        <div class="learning-section" id="demoSection" style="display: none;">
            <h2>🎮 互动演示：理解题目中的模块关系</h2>
            
            <div class="demo-area">
                <div class="modules-container">
                    <div class="module module-a" id="moduleA">A</div>
                    <div class="module module-b" id="moduleB">B</div>
                    <div class="module module-c" id="moduleC">C</div>
                    <div class="module module-d" id="moduleD">D</div>
                    <div class="module module-e" id="moduleE">E</div>
                    <div class="shared-data" id="sharedData">专用数据区</div>
                </div>
            </div>

            <div class="interface-table">
                <h3>📋 接口关系表</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background: #f7fafc;">
                        <th style="padding: 10px; border: 1px solid #e2e8f0;">编号</th>
                        <th style="padding: 10px; border: 1px solid #e2e8f0;">参数</th>
                        <th style="padding: 10px; border: 1px solid #e2e8f0;">返回值</th>
                    </tr>
                    <tr><td style="padding: 10px; border: 1px solid #e2e8f0;">1</td><td style="padding: 10px; border: 1px solid #e2e8f0;">数据项</td><td style="padding: 10px; border: 1px solid #e2e8f0;">数据项</td></tr>
                    <tr><td style="padding: 10px; border: 1px solid #e2e8f0;">2</td><td style="padding: 10px; border: 1px solid #e2e8f0;">数据项</td><td style="padding: 10px; border: 1px solid #e2e8f0;">数据项</td></tr>
                    <tr><td style="padding: 10px; border: 1px solid #e2e8f0;">3</td><td style="padding: 10px; border: 1px solid #e2e8f0;">功能码</td><td style="padding: 10px; border: 1px solid #e2e8f0;">无</td></tr>
                    <tr><td style="padding: 10px; border: 1px solid #e2e8f0;">4</td><td style="padding: 10px; border: 1px solid #e2e8f0;">无</td><td style="padding: 10px; border: 1px solid #e2e8f0;">列表</td></tr>
                </table>
            </div>

            <button class="btn" onclick="showCouplingAnalysis()">🔍 分析A和E的耦合关系</button>
        </div>

        <!-- 测验区域 -->
        <div class="quiz-section" id="quizSection" style="display: none;">
            <h2>🎯 现在来测试一下你的理解！</h2>
            <div class="quiz-question">
                根据图中的程序结构，模块A和模块E之间的耦合关系是什么？
            </div>
            
            <div class="quiz-options">
                <div class="quiz-option" data-answer="A">A. 公共耦合</div>
                <div class="quiz-option" data-answer="B">B. 数据耦合</div>
                <div class="quiz-option" data-answer="C">C. 内容耦合</div>
                <div class="quiz-option" data-answer="D">D. 无耦合</div>
            </div>

            <button class="btn" onclick="checkAnswer()">✅ 提交答案</button>
            
            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：A. 公共耦合</strong></p>
                <p>🎯 <strong>关键思路：</strong></p>
                <ul>
                    <li>观察图中，模块A和模块E都有虚线连接到"专用数据区"</li>
                    <li>这意味着A和E都要访问同一个共享的数据区域</li>
                    <li>当多个模块共享同一个全局数据区时，就形成了<strong>公共耦合</strong></li>
                    <li>虽然A和E之间没有直接的数据传递，但它们通过共享数据区产生了间接关系</li>
                </ul>
                <p>🚫 <strong>为什么不是数据耦合？</strong></p>
                <p>数据耦合是指模块间直接传递简单数据参数，而A和E之间没有直接的数据传递关系。</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let selectedAnswer = null;

        // 初始化动画
        gsap.from(".header", {duration: 1, y: -50, opacity: 0, ease: "bounce.out"});
        gsap.from(".learning-section", {duration: 1, y: 50, opacity: 0, stagger: 0.2, delay: 0.5});

        // 耦合类型卡片点击事件
        document.querySelectorAll('.coupling-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.coupling-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                
                // 添加动画效果
                gsap.to(this, {duration: 0.3, scale: 1.05, yoyo: true, repeat: 1});
            });
        });

        function updateProgress(step) {
            const progress = (step / 3) * 100;
            gsap.to("#progressFill", {duration: 0.5, width: progress + "%"});
        }

        function startDemo() {
            currentStep = 1;
            updateProgress(currentStep);
            
            document.getElementById('conceptSection').style.display = 'none';
            document.getElementById('demoSection').style.display = 'block';
            
            // 动画显示演示区域
            gsap.from("#demoSection", {duration: 0.8, x: 100, opacity: 0});
            
            // 模块动画
            gsap.from(".module", {duration: 1, scale: 0, stagger: 0.1, ease: "back.out(1.7)", delay: 0.5});
            gsap.from("#sharedData", {duration: 1, scale: 0, ease: "back.out(1.7)", delay: 1});
            
            // 添加脉冲效果
            setTimeout(() => {
                document.getElementById('moduleA').classList.add('pulse');
                document.getElementById('moduleE').classList.add('pulse');
                document.getElementById('sharedData').classList.add('glow');
            }, 2000);
        }

        function showCouplingAnalysis() {
            currentStep = 2;
            updateProgress(currentStep);
            
            // 显示连接线动画
            showConnectionLines();
            
            setTimeout(() => {
                document.getElementById('demoSection').style.display = 'none';
                document.getElementById('quizSection').style.display = 'block';
                gsap.from("#quizSection", {duration: 0.8, y: 100, opacity: 0});
            }, 3000);
        }

        function showConnectionLines() {
            const moduleA = document.getElementById('moduleA');
            const moduleE = document.getElementById('moduleE');
            const sharedData = document.getElementById('sharedData');
            
            // 创建连接线
            const lineA = document.createElement('div');
            lineA.className = 'connection-line';
            lineA.style.background = '#e53e3e';
            
            const lineE = document.createElement('div');
            lineE.className = 'connection-line';
            lineE.style.background = '#805ad5';
            
            document.querySelector('.modules-container').appendChild(lineA);
            document.querySelector('.modules-container').appendChild(lineE);
            
            // 计算位置和动画
            setTimeout(() => {
                gsap.to([lineA, lineE], {duration: 1, opacity: 1, width: "100px"});
            }, 500);
        }

        // 测验选项点击事件
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.quiz-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
                
                gsap.to(this, {duration: 0.2, scale: 1.05, yoyo: true, repeat: 1});
            });
        });

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            currentStep = 3;
            updateProgress(currentStep);
            
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                if (option.dataset.answer === 'A') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'A') {
                    option.classList.add('wrong');
                }
            });
            
            document.getElementById('explanation').style.display = 'block';
            gsap.from("#explanation", {duration: 0.8, y: 50, opacity: 0});
            
            // 庆祝动画
            if (selectedAnswer === 'A') {
                createCelebration();
            }
        }

        function createCelebration() {
            // 创建庆祝粒子效果
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.width = '10px';
                particle.style.height = '10px';
                particle.style.background = `hsl(${Math.random() * 360}, 70%, 60%)`;
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1000';
                
                document.body.appendChild(particle);
                
                gsap.set(particle, {
                    x: window.innerWidth / 2,
                    y: window.innerHeight / 2
                });
                
                gsap.to(particle, {
                    duration: 2,
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    opacity: 0,
                    scale: 0,
                    ease: "power2.out",
                    onComplete: () => particle.remove()
                });
            }
        }

        // 添加键盘交互
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && currentStep === 0) {
                startDemo();
            } else if (e.key === 'Enter' && currentStep === 1) {
                showCouplingAnalysis();
            } else if (e.key === 'Enter' && currentStep === 2 && selectedAnswer) {
                checkAnswer();
            }
        });
    </script>
</body>
</html>
